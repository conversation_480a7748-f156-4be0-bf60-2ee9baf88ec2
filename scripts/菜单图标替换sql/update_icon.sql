UPDATE sys_menu SET icon = 'eos-icons:system-group' WHERE menu_id = 1;
UPDATE sys_menu SET icon = 'solar:monitor-camera-outline' WHERE menu_id = 2;
UPDATE sys_menu SET icon = 'ant-design:tool-outlined' WHERE menu_id = 3;
UPDATE sys_menu SET icon = 'flat-color-icons:plus' WHERE menu_id = 4;
UPDATE sys_menu SET icon = 'devicon:vscode' WHERE menu_id = 5;
UPDATE sys_menu SET icon = 'ph:users-light' WHERE menu_id = 6;
UPDATE sys_menu SET icon = 'ant-design:user-outlined' WHERE menu_id = 100;
UPDATE sys_menu SET icon = 'eos-icons:role-binding-outlined' WHERE menu_id = 101;
UPDATE sys_menu SET icon = 'ic:sharp-menu' WHERE menu_id = 102;
UPDATE sys_menu SET icon = 'mingcute:department-line' WHERE menu_id = 103;
UPDATE sys_menu SET icon = 'icon-park-outline:appointment' WHERE menu_id = 104;
UPDATE sys_menu SET icon = 'fluent-mdl2:dictionary' WHERE menu_id = 105;
UPDATE sys_menu SET icon = 'ant-design:setting-outlined' WHERE menu_id = 106;
UPDATE sys_menu SET icon = 'fe:notice-push' WHERE menu_id = 107;
UPDATE sys_menu SET icon = 'material-symbols:logo-dev-outline' WHERE menu_id = 108;
UPDATE sys_menu SET icon = 'material-symbols:generating-tokens-outline' WHERE menu_id = 109;
UPDATE sys_menu SET icon = 'devicon:redis-wordmark' WHERE menu_id = 113;
UPDATE sys_menu SET icon = 'fluent:form-new-24-regular' WHERE menu_id = 114;
UPDATE sys_menu SET icon = 'tabler:code' WHERE menu_id = 115;
UPDATE sys_menu SET icon = 'devicon:spring-wordmark' WHERE menu_id = 117;
UPDATE sys_menu SET icon = 'solar:folder-with-files-outline' WHERE menu_id = 118;
UPDATE sys_menu SET icon = 'svg:snail-job' WHERE menu_id = 120;
UPDATE sys_menu SET icon = 'ph:user-list' WHERE menu_id = 121;
UPDATE sys_menu SET icon = 'bx:package' WHERE menu_id = 122;
UPDATE sys_menu SET icon = 'solar:monitor-smartphone-outline' WHERE menu_id = 123;
UPDATE sys_menu SET icon = 'arcticons:one-hand-operation' WHERE menu_id = 500;
UPDATE sys_menu SET icon = 'streamline:interface-login-dial-pad-finger-password-dial-pad-dot-finger' WHERE menu_id = 501;

UPDATE sys_menu SET icon = 'lucide:table' WHERE menu_id = 1500;
UPDATE sys_menu SET icon = 'emojione:evergreen-tree' WHERE menu_id = 1506;

/*UPDATE sys_menu SET icon = 'icon-park-twotone:web-page' WHERE menu_id = 4; PLUS官网 */
UPDATE sys_menu SET icon = 'mdi:workflow-outline' WHERE menu_id = 11616;
UPDATE sys_menu SET icon = 'carbon:model-alt' WHERE menu_id = 11617;
UPDATE sys_menu SET icon = 'carbon:task-approved' WHERE menu_id = 11618;
UPDATE sys_menu SET icon = 'ri:todo-line' WHERE menu_id = 11619;
UPDATE sys_menu SET icon = 'fluent-mdl2:build-definition' WHERE menu_id = 11620;
UPDATE sys_menu SET icon = 'ri:instance-line' WHERE menu_id = 11621;
UPDATE sys_menu SET icon = 'tabler:category-plus' WHERE menu_id = 11622;
UPDATE sys_menu SET icon = 'ic:round-launch' WHERE menu_id = 11629;
UPDATE sys_menu SET icon = 'icon-park-outline:monitor' WHERE menu_id = 11630;
UPDATE sys_menu SET icon = 'ri:todo-line' WHERE menu_id = 11631;
UPDATE sys_menu SET icon = 'material-symbols:cloud-done-outline-rounded' WHERE menu_id = 11632;
UPDATE sys_menu SET icon = 'mdi:cc-outline' WHERE menu_id = 11633;
UPDATE sys_menu SET icon = 'fluent-mdl2:leave-user' WHERE menu_id = 11638;
UPDATE sys_menu SET icon = 'fluent:form-24-regular' WHERE menu_id = 11628;
/* 从本地迁移菜单管理的跳转菜单 */
UPDATE sys_menu SET icon = 'tabler:code' WHERE menu_id = 116;
UPDATE sys_menu SET icon = 'eos-icons:role-binding-outlined' WHERE menu_id = 130;
UPDATE sys_menu SET icon = 'ant-design:setting-outlined' WHERE menu_id = 133;
UPDATE sys_menu SET icon = 'ant-design:setting-outlined' WHERE menu_id = 133;
UPDATE sys_menu SET icon = 'fluent-mdl2:flow' WHERE menu_id = 11700;
UPDATE sys_menu SET icon = 'flat-color-icons:leave' WHERE menu_id = 11701;

