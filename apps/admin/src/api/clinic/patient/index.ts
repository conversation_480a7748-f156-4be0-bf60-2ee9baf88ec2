import type { PatientsForm, PatientsQuery, PatientsVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询患者档案列表
 * @param params
 * @returns 患者档案列表
 */
export function patientsList(params?: PatientsQuery) {
  return requestClient.get<PageResult<PatientsVO>>('/system/patients/list', {
    params,
  });
}

/**
 * 导出患者档案列表
 * @param params
 * @returns 患者档案列表
 */
export function patientsExport(params?: PatientsQuery) {
  return commonExport('/system/patients/export', params ?? {});
}

/**
 * 查询患者档案详情
 * @param id id
 * @returns 患者档案详情
 */
export function patientsInfo(id: ID) {
  return requestClient.get<PatientsVO>(`/system/patients/${id}`);
}

/**
 * 新增患者档案
 * @param data
 * @returns void
 */
export function patientsAdd(data: PatientsForm) {
  return requestClient.postWithMsg<void>('/system/patients', data);
}

/**
 * 更新患者档案
 * @param data
 * @returns void
 */
export function patientsUpdate(data: PatientsForm) {
  return requestClient.putWithMsg<void>('/system/patients', data);
}

/**
 * 删除患者档案
 * @param id id
 * @returns void
 */
export function patientsRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/system/patients/${id}`);
}
