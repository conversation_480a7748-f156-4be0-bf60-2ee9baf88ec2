import type { BaseEntity, PageQuery } from '#/api/common';

export interface InventoryStockInItemVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 入库单ID（关联qili_inventory_stock_in.id）
   */
  stockInId: string | number;

  /**
   * 药品ID（qili_drug.id）
   */
  drugId: string | number;

  /**
   * 批次号（如20250724-001）
   */
  batchNo: string;

  /**
   * 进价（分）
   */
  purchasePrice: number;

  /**
   * 入库数量
   */
  quantity: number;

  /**
   * 小计金额（分）
   */
  amount: number;

  /**
   * 备注
   */
  remark: string;
}

export interface InventoryStockInItemForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 入库单ID（关联qili_inventory_stock_in.id）
   */
  stockInId?: string | number;

  /**
   * 药品ID（qili_drug.id）
   */
  drugId?: string | number;

  /**
   * 批次号（如20250724-001）
   */
  batchNo?: string;

  /**
   * 进价（分）
   */
  purchasePrice?: number;

  /**
   * 入库数量
   */
  quantity?: number;

  /**
   * 小计金额（分）
   */
  amount?: number;

  /**
   * 备注
   */
  remark?: string;
}

export interface InventoryStockInItemQuery extends PageQuery {
  /**
   * 入库单ID（关联qili_inventory_stock_in.id）
   */
  stockInId?: string | number;

  /**
   * 药品ID（qili_drug.id）
   */
  drugId?: string | number;

  /**
   * 批次号（如20250724-001）
   */
  batchNo?: string;

  /**
   * 进价（分）
   */
  purchasePrice?: number;

  /**
   * 入库数量
   */
  quantity?: number;

  /**
   * 小计金额（分）
   */
  amount?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
