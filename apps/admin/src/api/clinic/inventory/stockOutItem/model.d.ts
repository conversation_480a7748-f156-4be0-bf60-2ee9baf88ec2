import type { PageQuery, BaseEntity } from '#/api/common';

export interface InventoryStockOutItemVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 出库单ID（关联qili_inventory_stock_out.id）
   */
  stockOutId: string | number;

  /**
   * 药品ID（qili_drug.id）
   */
  drugId: string | number;

  /**
   * 出库批次号（可选）
   */
  batchNo: string;

  /**
   * 出库价格（分）
   */
  sellPrice: number;

  /**
   * 出库数量
   */
  quantity: number;

  /**
   * 小计金额（分）
   */
  amount: number;

  /**
   * 备注
   */
  remark: string;

}

export interface InventoryStockOutItemForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 出库单ID（关联qili_inventory_stock_out.id）
   */
  stockOutId?: string | number;

  /**
   * 药品ID（qili_drug.id）
   */
  drugId?: string | number;

  /**
   * 出库批次号（可选）
   */
  batchNo?: string;

  /**
   * 出库价格（分）
   */
  sellPrice?: number;

  /**
   * 出库数量
   */
  quantity?: number;

  /**
   * 小计金额（分）
   */
  amount?: number;

  /**
   * 备注
   */
  remark?: string;

}

export interface InventoryStockOutItemQuery extends PageQuery {
  /**
   * 出库单ID（关联qili_inventory_stock_out.id）
   */
  stockOutId?: string | number;

  /**
   * 药品ID（qili_drug.id）
   */
  drugId?: string | number;

  /**
   * 出库批次号（可选）
   */
  batchNo?: string;

  /**
   * 出库价格（分）
   */
  sellPrice?: number;

  /**
   * 出库数量
   */
  quantity?: number;

  /**
   * 小计金额（分）
   */
  amount?: number;

  /**
    * 日期范围参数
    */
  params?: any;
}
