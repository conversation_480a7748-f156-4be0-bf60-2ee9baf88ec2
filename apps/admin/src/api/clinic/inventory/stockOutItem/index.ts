import type { InventoryStockOutItemVO, InventoryStockOutItemForm, InventoryStockOutItemQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询库存出库子列表
* @param params
* @returns 库存出库子列表
*/
export function inventoryStockOutItemList(params?: InventoryStockOutItemQuery) {
  return requestClient.get<PageResult<InventoryStockOutItemVO>>('/clinic/inventoryStockOutItem/list', { params });
}

/**
 * 导出库存出库子列表
 * @param params
 * @returns 库存出库子列表
 */
export function inventoryStockOutItemExport(params?: InventoryStockOutItemQuery) {
  return commonExport('/clinic/inventoryStockOutItem/export', params ?? {});
}

/**
 * 查询库存出库子详情
 * @param id id
 * @returns 库存出库子详情
 */
export function inventoryStockOutItemInfo(id: ID) {
  return requestClient.get<InventoryStockOutItemVO>(`/clinic/inventoryStockOutItem/${id}`);
}

/**
 * 新增库存出库子
 * @param data
 * @returns void
 */
export function inventoryStockOutItemAdd(data: InventoryStockOutItemForm) {
  return requestClient.postWithMsg<void>('/clinic/inventoryStockOutItem', data);
}

/**
 * 更新库存出库子
 * @param data
 * @returns void
 */
export function inventoryStockOutItemUpdate(data: InventoryStockOutItemForm) {
  return requestClient.putWithMsg<void>('/clinic/inventoryStockOutItem', data);
}

/**
 * 删除库存出库子
 * @param id id
 * @returns void
 */
export function inventoryStockOutItemRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/clinic/inventoryStockOutItem/${id}`);
}
