import type { InventoryStockOutForm, InventoryStockOutQuery, InventoryStockOutVO, } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询出库列表
 * @param params
 * @returns 出库列表
 */
export function inventoryStockOutList(params?: InventoryStockOutQuery) {
  return requestClient.get<PageResult<InventoryStockOutVO>>(
    '/clinic/inventoryStockOut/list',
    { params },
  );
}

/**
 * 导出出库列表
 * @param params
 * @returns 出库列表
 */
export function inventoryStockOutExport(params?: InventoryStockOutQuery) {
  return commonExport('/clinic/inventoryStockOut/export', params ?? {});
}

/**
 * 查询库存出库主详情
 * @param id id
 * @returns 库存出库主详情
 */
export function inventoryStockOutInfo(id: ID) {
  return requestClient.get<InventoryStockOutVO>(
    `/clinic/inventoryStockOut/${id}`,
  );
}

/**
 * 新增库存出库主
 * @param data
 * @returns void
 */
export function inventoryStockOutAdd(data: InventoryStockOutForm) {
  return requestClient.postWithMsg<void>('/clinic/inventoryStockOut', data);
}

/**
 * 更新库存出库主
 * @param data
 * @returns void
 */
export function inventoryStockOutUpdate(data: InventoryStockOutForm) {
  return requestClient.putWithMsg<void>('/clinic/inventoryStockOut', data);
}

/**
 * 删除库存出库主
 * @param id id
 * @returns void
 */
export function inventoryStockOutRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/clinic/inventoryStockOut/${id}`);
}
