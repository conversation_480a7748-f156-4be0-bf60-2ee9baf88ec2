import type {
  MedicalFieldVO,
  MedicalFieldForm,
  MedicalFieldQuery,
} from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询病历字段字典列表
 * @param params
 * @returns 病历字段字典列表
 */
export function medicalFieldList(params?: MedicalFieldQuery) {
  return requestClient.get<PageResult<MedicalFieldVO>>(
    '/clinic/medicalField/list',
    { params },
  );
}

/**
 * 导出病历字段字典列表
 * @param params
 * @returns 病历字段字典列表
 */
export function medicalFieldExport(params?: MedicalFieldQuery) {
  return commonExport('/clinic/medicalField/export', params ?? {});
}

/**
 * 查询病历字段字典详情
 * @param id id
 * @returns 病历字段字典详情
 */
export function medicalFieldInfo(id: ID) {
  return requestClient.get<MedicalFieldVO>(`/clinic/medicalField/${id}`);
}

/**
 * 新增病历字段字典
 * @param data
 * @returns void
 */
export function medicalFieldAdd(data: MedicalFieldForm) {
  return requestClient.postWithMsg<void>('/clinic/medicalField', data);
}

/**
 * 更新病历字段字典
 * @param data
 * @returns void
 */
export function medicalFieldUpdate(data: MedicalFieldForm) {
  return requestClient.putWithMsg<void>('/clinic/medicalField', data);
}

/**
 * 删除病历字段字典
 * @param id id
 * @returns void
 */
export function medicalFieldRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/clinic/medicalField/${id}`);
}
