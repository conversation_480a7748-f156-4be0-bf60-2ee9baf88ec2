import type {
  MedicalTemplateForm,
  MedicalTemplateQuery,
  MedicalTemplateVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询病历模板列表
 * @param params
 * @returns 病历模板列表
 */
export function medicalTemplateList(params?: MedicalTemplateQuery) {
  return requestClient.get<PageResult<MedicalTemplateVO>>(
    '/clinic/medicalTemplate/list',
    { params },
  );
}

/**
 * 导出病历模板列表
 * @param params
 * @returns 病历模板列表
 */
export function medicalTemplateExport(params?: MedicalTemplateQuery) {
  return commonExport('/clinic/medicalTemplate/export', params ?? {});
}

/**
 * 查询病历模板详情
 * @param id id
 * @returns 病历模板详情
 */
export function medicalTemplateInfo(id: ID) {
  return requestClient.get<MedicalTemplateVO>(`/clinic/medicalTemplate/${id}`);
}

/**
 * 新增病历模板
 * @param data
 * @returns void
 */
export function medicalTemplateAdd(data: MedicalTemplateForm) {
  return requestClient.postWithMsg<void>('/clinic/medicalTemplate', data);
}

/**
 * 更新病历模板
 * @param data
 * @returns void
 */
export function medicalTemplateUpdate(data: MedicalTemplateForm) {
  return requestClient.putWithMsg<void>('/clinic/medicalTemplate', data);
}

/**
 * 删除病历模板
 * @param id id
 * @returns void
 */
export function medicalTemplateRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/clinic/medicalTemplate/${id}`);
}

/**
 * 查询默认病历模板
 * @returns 默认病历模板
 */
export function medicalDefaultTemplateInfo() {
  return requestClient.get<MedicalTemplateVO>(
    '/clinic/medicalTemplate/default',
  );
}
