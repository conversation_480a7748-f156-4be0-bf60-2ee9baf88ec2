import type { BaseEntity, PageQuery } from '#/api/common';

export interface MedicalFieldDataVO {
  /**
   * 主键ID
   */
  id: number | string;

  /**
   * 分组名称
   */
  groupName: string;

  /**
   * 字段ID（关联qili_medical_field.id）
   */
  fieldId: number | string;

  /**
   * 字段值（如 咳嗽）
   */
  data: string;

  /**
   * 显示顺序
   */
  sortOrder: number;

  /**
   * 是否系统默认值（1是 0否）
   */
  defaultFlag: string;
}

export interface MedicalFieldDataForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: number | string;

  /**
   * 分组名称
   */
  groupName?: string;

  /**
   * 字段ID（关联qili_medical_field.id）
   */
  fieldId?: number | string;

  /**
   * 字段值（如 咳嗽）
   */
  data?: string;

  /**
   * 显示顺序
   */
  sortOrder?: number;

  /**
   * 是否系统默认值（1是 0否）
   */
  defaultFlag?: string;
}

export interface MedicalFieldDataQuery extends PageQuery {
  /**
   * 字段值（如 咳嗽）
   */
  data?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
