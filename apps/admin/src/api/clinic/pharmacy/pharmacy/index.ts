import type { PharmacyVO, PharmacyForm, PharmacyQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询药房信息列表
* @param params
* @returns 药房信息列表
*/
export function pharmacyList(params?: PharmacyQuery) {
  return requestClient.get<PageResult<PharmacyVO>>('/clinic/pharmacy/list', { params });
}

/**
 * 导出药房信息列表
 * @param params
 * @returns 药房信息列表
 */
export function pharmacyExport(params?: PharmacyQuery) {
  return commonExport('/clinic/pharmacy/export', params ?? {});
}

/**
 * 查询药房信息详情
 * @param id id
 * @returns 药房信息详情
 */
export function pharmacyInfo(id: ID) {
  return requestClient.get<PharmacyVO>(`/clinic/pharmacy/${id}`);
}

/**
 * 新增药房信息
 * @param data
 * @returns void
 */
export function pharmacyAdd(data: PharmacyForm) {
  return requestClient.postWithMsg<void>('/clinic/pharmacy', data);
}

/**
 * 更新药房信息
 * @param data
 * @returns void
 */
export function pharmacyUpdate(data: PharmacyForm) {
  return requestClient.putWithMsg<void>('/clinic/pharmacy', data);
}

/**
 * 删除药房信息
 * @param id id
 * @returns void
 */
export function pharmacyRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/clinic/pharmacy/${id}`);
}
