import type { PageQuery, BaseEntity } from '#/api/common';

export interface PharmacyVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 药房名称
   */
  name: string;

  /**
   * 药房编码
   */
  code: string;

  /**
   * 负责人
   */
  contactPerson: string;

  /**
   * 状态
   */
  status: string;

}

export interface PharmacyForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 药房名称
   */
  name?: string;

  /**
   * 药房编码
   */
  code?: string;

  /**
   * 所在位置
   */
  location?: string;

  /**
   * 负责人
   */
  contactPerson?: string;

  /**
   * 联系电话
   */
  contactPhone?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface PharmacyQuery extends PageQuery {
  /**
   * 药房名称
   */
  name?: string;

  /**
   * 药房编码
   */
  code?: string;

  /**
   * 所在位置
   */
  location?: string;

  /**
   * 负责人
   */
  contactPerson?: string;

  /**
   * 联系电话
   */
  contactPhone?: string;

  /**
   * 状态
   */
  status?: string;

  /**
    * 日期范围参数
    */
  params?: any;
}
