import type { BaseEntity, PageQuery } from '#/api/common';

export interface DrugVO {
  /**
   * 主键ID，自增
   */
  id: number | string;

  /**
   * 药品名称
   */
  name: string;

  /**
   * 拼音码（支持模糊搜索）
   */
  alias?: string;

  /**
   * 药品编码
   */
  code: string;

  /**
   * 规格（如0.25g*10片）
   */
  specification: string;

  /**
   * 单位（g/克/片/袋/丸等）
   */
  unit: string;

  /**
   * 药品分类（中药/西药/成药/草药）
   */
  category: string;

  /**
   * 单价（元）
   */
  price: number;

  /**
   * 当前库存数量
   */
  stock: number;

  /**
   * 库存单位（克/剂/盒等）
   */
  stockUnit: string;
}

export interface DrugForm extends BaseEntity {
  /**
   * 主键ID，自增
   */
  id?: number | string;

  /**
   * 药品名称
   */
  name?: string;

  /**
   * 拼音码（支持模糊搜索）
   */
  alias?: string;

  /**
   * 药品编码
   */
  code?: string;

  /**
   * 规格（如0.25g*10片）
   */
  specification?: string;

  /**
   * 单位（g/克/片/袋/丸等）
   */
  unit?: string;

  /**
   * 生产厂家
   */
  manufacturer?: string;

  /**
   * 药品分类（中药/西药/成药/草药）
   */
  category?: string;

  /**
   * 单价（元）
   */
  price?: number;

  /**
   * 备注
   */
  remark?: string;
}

export interface DrugQuery extends PageQuery {
  /**
   * 药品名称
   */
  name?: string;

  /**
   * 药品分类（中药/西药/成药/草药）
   */
  category?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
