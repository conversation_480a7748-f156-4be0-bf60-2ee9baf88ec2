import type { DrugForm, DrugQuery, DrugVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询药品信息列表
* @param params
* @returns 药品信息列表
*/
export function drugList(params?: DrugQuery) {
  return requestClient.get<PageResult<DrugVO>>('/clinic/drug/list', { params });
}

/**
 * 导出药品信息列表
 * @param params
 * @returns 药品信息列表
 */
export function drugExport(params?: DrugQuery) {
  return commonExport('/clinic/drug/export', params ?? {});
}

/**
 * 查询药品信息详情
 * @param id id
 * @returns 药品信息详情
 */
export function drugInfo(id: ID) {
  return requestClient.get<DrugVO>(`/clinic/drug/${id}`);
}

/**
 * 新增药品信息
 * @param data
 * @returns void
 */
export function drugAdd(data: DrugForm) {
  return requestClient.postWithMsg<void>('/clinic/drug', data);
}

/**
 * 更新药品信息
 * @param data
 * @returns void
 */
export function drugUpdate(data: DrugForm) {
  return requestClient.putWithMsg<void>('/clinic/drug', data);
}

/**
 * 删除药品信息
 * @param id id
 * @returns void
 */
export function drugRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/clinic/drug/${id}`);
}
