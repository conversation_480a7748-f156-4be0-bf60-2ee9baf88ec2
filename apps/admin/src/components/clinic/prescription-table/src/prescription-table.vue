<script setup lang="ts">
import type { VxeTablePropTypes } from 'vxe-table';
import type { DrugForm, DrugVO } from '#/api/clinic/pharmacy/drug/model';

import { ref } from 'vue';

import { Button, InputNumber } from 'ant-design-vue';

import { DrugSelect } from '#/components/clinic/drug-select';
import { buildShortUUID } from '@vben/utils';

interface DrugItem {
  id: string;
  name: string;
  code: string;
  price: number;
  unit: string;
  amount: number;
  priceCount: number | string;
}

const tableData = ref<DrugItem[]>([
  {
    id: buildShortUUID(),
    name: '',
    code: '',
    price: 0,
    unit: '',
    amount: 0,
    priceCount: 0,
  },
]);

const editConfig = ref<VxeTablePropTypes.EditConfig>({
  trigger: 'click',
  mode: 'row',
  showStatus: true,
});

const addRow = () => {
  tableData.value.push({
    id: buildShortUUID(),
    name: '',
    code: '',
    price: 0,
    unit: 'g',
    amount: 0,
    priceCount: 0,
  });
};

// 处理药品选择
const handleDrugSelect = (drug: DrugItem, row: DrugVO) => {
  console.log(drug);
  row.name = drug.name;
  row.code = drug.code;
  row.price = drug.price;
  row.unit = drug.unit;
  row.priceCount = drug.price * row.amount || 0;
};

const removeRow = (row: Medicine) => {
  const index = tableData.value.findIndex((item) => item.id === row.id);
  if (index !== -1) {
    tableData.value.splice(index, 1);
  }
};

// 暴露方法给父组件
defineExpose({
  addRow,
});
</script>

<template>
  <div>
    <vxe-table
      :data="tableData"
      :edit-config="editConfig"
      border
      stripe
      size="small"
      align="center"
    >
      <vxe-column type="seq" width="60" title="序号" />
      <vxe-column
        field="name"
        title="药品名称"
        :edit-render="{ autoFocus: true, placeholder: '请点击此处选择药品...' }"
      >
        <template #edit="{ row }">
          <DrugSelect
            v-model:value="row.name"
            class="w-full"
            @select="(drug) => handleDrugSelect(drug, row)"
          />
        </template>
      </vxe-column>
      <vxe-column field="code" title="药品编码" width="120" />
      <vxe-column field="price" title="单价(元)" width="120" />
      <vxe-column field="amount" title="数量" :edit-render="{}">
        <template #edit="{ row }">
          <InputNumber
            v-model:value="row.amount"
            type="number"
            placeholder="请输入克数"
            :min="0"
            class="w-full"
          />
        </template>
      </vxe-column>
      <vxe-column field="unit" title="单位" width="80" />
      <vxe-column field="price" title="价格(元)">
        <template #default="{ row }">
          {{ (row.price * row.amount).toFixed(2) }}
        </template>
      </vxe-column>
      <vxe-column title="操作" width="100" fixed="right">
        <template #default="{ row }">
          <Button
            type="link"
            danger
            size="small"
            class="flex items-center"
            @click="removeRow(row)"
          >
            <i
              class="icon-[material-symbols-light--delete-outline-rounded] mr-1"
            ></i>
            删除
          </Button>
        </template>
      </vxe-column>
    </vxe-table>
    <div class="mt-4 flex justify-end text-sm">
      <div class="flex text-gray-500">
        <span>总价：</span>
        <div class="text-primary">
          {{ tableData.reduce((sum, item) => sum + item.price, 0).toFixed(2) }}
        </div>
        <span class="ml-1">元</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.vxe-table) {
  border-radius: 8px;
}
</style>
