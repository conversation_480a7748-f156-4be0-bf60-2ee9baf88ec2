<script setup lang="ts">
import type { DrugVO } from '#/api/clinic/pharmacy/drug/model';

import { computed, ref, watch } from 'vue';

import { useDebounceFn } from '@vueuse/core';
import { Select, SelectOption, Spin } from 'ant-design-vue';

import { drugList } from '#/api/clinic/pharmacy/drug';

const props = defineProps<{
  // 支持返回完整药品对象
  drug?: DrugVO;
  value?: string;
}>();

const emit = defineEmits<{
  (e: 'update:value', value: string): void;
  (e: 'update:drug', drug: DrugVO | undefined): void;
  (e: 'select', drug: DrugVO): void;
}>();

const loading = ref(false);
const keyword = ref('');
const options = ref<DrugVO[]>([]);

const modelValue = computed({
  get: () => props.value || '',
  set: (value: string) => emit('update:value', value),
});

const searchDrugs = async (value: string) => {
  keyword.value = value;
  if (!value) {
    options.value = [];
    return;
  }

  loading.value = true;
  try {
    const res: any = await drugList({
      searchValue: value,
      pageSize: 20,
    });
    options.value = res.rows || [];
  } finally {
    loading.value = false;
  }
};

// 使用防抖函数，延迟300ms执行搜索
const handleSearch = useDebounceFn(searchDrugs, 300);

// 处理选择药品
const handleSelect = (value: string) => {
  const selectedDrug = options.value.find((item) => item.name === value);
  if (selectedDrug) {
    emit('update:drug', selectedDrug);
    emit('select', selectedDrug);
  }
};

// 监听外部value变化，如果有值但options为空，则搜索一次
watch(
  () => props.value,
  (value) => {
    if (value && options.value.length === 0) {
      // 初始化时直接调用搜索，不需要防抖
      searchDrugs(value);
    }
  },
  { immediate: true },
);
</script>

<template>
  <Select
    v-model:value="modelValue"
    show-search
    :filter-option="false"
    :loading="loading"
    placeholder="请输入药品名称搜索"
    @search="handleSearch"
    @select="handleSelect"
  >
    <template v-for="item in options" :key="item.id">
      <SelectOption :value="item.name">
        <div class="flex w-full items-center justify-between">
          <span class="text-base text-gray-900">{{ item.name }}</span>
          <span class="text-xs text-gray-400">{{ item.code }}</span>
        </div>
      </SelectOption>
    </template>

    <template #notFoundContent>
      <Spin v-if="loading" size="small" />
      <span v-else>{{ keyword ? '未找到相关药品' : '请输入关键词搜索' }}</span>
    </template>
  </Select>
</template>

<style scoped>
:deep(.ant-select) {
  width: 100%;
}
</style>
