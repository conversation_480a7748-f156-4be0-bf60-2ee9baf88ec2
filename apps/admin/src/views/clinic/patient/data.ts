import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import dayjs from 'dayjs';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'name',
    label: '患者姓名',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  // {
  //   title: '主键ID',
  //   field: 'id',
  // },
  // 添加一列，男性显示红色头像，女性显示蓝色头像
  // {
  //   title: '头像',
  //   field: 'avatar',
  //   slots: {
  //     default: ({ row }) => {
  //       return renderIcon(
  //         row.gender === '1'
  //           ? 'streamline-kameleon-color:man-15'
  //           : 'streamline-kameleon-color:woman-15',
  //       );
  //     },
  //   },
  //   align: 'center',
  // },
  {
    title: '患者姓名',
    field: 'name',
  },
  {
    title: '性别',
    field: 'gender',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.QILI_PATIENTS_GENDER 便于维护
        return renderDict(row.gender, 'qili_patients_gender');
      },
    },
  },
  {
    title: '年龄',
    field: 'ageYear',
  },
  {
    title: '出生日期',
    field: 'dateOfBirth',
    formatter: ({ row }) => {
      return dayjs(row.dateOfBirth).format('YYYY-MM-DD');
    },
  },
  {
    title: '手机号',
    field: 'phone',
  },
  {
    title: '省',
    field: 'addressProvince',
  },
  {
    title: '市',
    field: 'addressCity',
  },
  {
    title: '区/县',
    field: 'addressDistrict',
  },
  {
    title: '详细地址',
    field: 'addressDetail',
  },
  {
    title: '标签',
    field: 'tags',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    label: '主键ID，自增',
    fieldName: 'id',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '患者姓名',
    fieldName: 'name',
    component: 'Input',
  },
  {
    label: '性别',
    fieldName: 'gender',
    component: 'RadioGroup',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.QILI_PATIENTS_GENDER 便于维护
      options: getDictOptions('qili_patients_gender'),
      buttonStyle: 'solid',
      optionType: 'button',
    },
    rules: 'selectRequired',
  },
  {
    label: '年龄-年',
    fieldName: 'ageYear',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '年龄-月',
    fieldName: 'ageMonth',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '年龄-日',
    fieldName: 'ageDay',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '出生日期',
    fieldName: 'dateOfBirth',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '手机号',
    fieldName: 'phone',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '身份证号/证件号',
    fieldName: 'idCard',
    component: 'Input',
  },
  {
    label: '患者来源',
    fieldName: 'source',
    component: 'Input',
  },
  {
    label: '婚姻状况',
    fieldName: 'maritalStatus',
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      optionType: 'button',
    },
  },
  {
    label: '体重',
    fieldName: 'weight',
    component: 'Input',
  },
  {
    label: '省',
    fieldName: 'addressProvince',
    component: 'Input',
  },
  {
    label: '市',
    fieldName: 'addressCity',
    component: 'Input',
  },
  {
    label: '区/县',
    fieldName: 'addressDistrict',
    component: 'Input',
  },
  {
    label: '详细地址',
    fieldName: 'addressDetail',
    component: 'Input',
  },
  {
    label: '职业',
    fieldName: 'occupation',
    component: 'Input',
  },
  {
    label: '档案号',
    fieldName: 'medicalRecordNo',
    component: 'Input',
  },
  {
    label: '工作单位',
    fieldName: 'employer',
    component: 'Input',
  },
  {
    label: '民族',
    fieldName: 'ethnicity',
    component: 'Input',
  },
  {
    label: '备注',
    fieldName: 'remark',
    component: 'Textarea',
  },
  {
    label: '到店原因',
    fieldName: 'visitReason',
    component: 'Input',
  },
  {
    label: '既往史',
    fieldName: 'pastHistory',
    component: 'Textarea',
  },
  {
    label: '过敏史',
    fieldName: 'allergyHistory',
    component: 'Textarea',
  },
  {
    label: '标签',
    fieldName: 'tags',
    component: 'Input',
  },
];
