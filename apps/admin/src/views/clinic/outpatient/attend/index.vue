<script setup lang="ts">
import type { VxeTablePropTypes } from 'vxe-table';

import type { MedicalTemplateVO } from '#/api/clinic/medical/template/model';

import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import {
  Avatar,
  Button,
  Card,
  CheckableTag,
  Empty,
  InputSearch,
  Modal,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import { medicalDefaultTemplateInfo } from '#/api/clinic/medical/template';
import { isTrue } from '#/utils/cis-common';

import PrescriptionTable from '../../../../components/clinic/prescription-table/src/prescription-table.vue';
import PatientList from './components/PatientList.vue';

interface Patient {
  name: string;
  id: string;
  phone: string;
  gender: string;
  department: string;
  age: number;
  address: string;
  status?: 'completed' | 'pending' | 'processing';
}

const currentPatient = ref<null | Patient>(null);
const prescriptionTable = ref();

const handlePatientSelect = (patient: Patient) => {
  currentPatient.value = patient;
};

const handleAddRow = () => {
  prescriptionTable.value?.addRow();
};

const defaultTemplate = ref<MedicalTemplateVO>({
  id: '',
  name: '',
  type: '',
  directory: '',
  description: '',
  fieldList: [],
});

// 快速选择相关状态
const modalVisible = ref<Record<number | string, boolean>>({});
const currentRow = ref<any>(null);
const searchKeyword = ref('');
const selectedValues = ref<string[]>([]);

const showModal = (row: any) => {
  currentRow.value = row;
  modalVisible.value[row.id] = true;
  searchKeyword.value = ''; // 重置搜索关键词
  // 初始化已选值
  selectedValues.value = row.fieldValue ? row.fieldValue.split(',') : [];
};

const handleTagSelect = (item: any, checked: boolean) => {
  if (checked) {
    if (!selectedValues.value.includes(item.data)) {
      selectedValues.value.push(item.data);
    }
  } else {
    const index = selectedValues.value.indexOf(item.data);
    if (index !== -1) {
      selectedValues.value.splice(index, 1);
    }
  }
};

const handleConfirm = () => {
  if (currentRow.value) {
    currentRow.value.fieldValue = selectedValues.value.join(',');
    modalVisible.value[currentRow.value.id] = false;
  }
};

const getGroupedData = (fieldDataList: any[]) => {
  const groups: Record<string, any[]> = {};
  const filteredList =
    fieldDataList?.filter(
      (item) =>
        !searchKeyword.value ||
        item.data.toLowerCase().includes(searchKeyword.value.toLowerCase()),
    ) || [];

  for (const item of filteredList) {
    const groupName = item.groupName || '其他';
    if (!groups[groupName]) {
      groups[groupName] = [];
    }
    groups[groupName].push(item);
  }
  return groups;
};

const editConfig = ref<VxeTablePropTypes.EditConfig>({
  trigger: 'click',
  mode: 'cell',
  showStatus: true,
});

const tabList = ref([
  {
    key: '1',
    label: '历史病历',
  },
  {
    key: '2',
    label: '历史处方',
  },
]);

const getTemplate = async () => {
  const res = await medicalDefaultTemplateInfo();
  defaultTemplate.value = res;
};
getTemplate();

const handleDrugSelect = (drug: any) => {
  console.log('drug', drug);
};
</script>

<template>
  <Page :auto-content-height="true">
    <div class="flex h-full w-full gap-[8px]">
      <Card size="small" class="w-[15%]">
        <PatientList
          :selected-id="currentPatient?.id"
          @select="handlePatientSelect"
        />
      </Card>
      <!-- 中部内容 -->
      <div class="flex w-[65%] flex-1 flex-col gap-2">
        <Card size="small">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <Avatar
                :size="48"
                :class="
                  currentPatient?.gender === '男'
                    ? 'man-avatar'
                    : 'woman-avatar'
                "
              >
                {{ currentPatient?.name?.charAt(0) }}
              </Avatar>
              <div class="flex flex-col">
                <span class="text-base font-medium">
                  {{ currentPatient?.name || '--' }}
                </span>
                <div class="flex items-center gap-2">
                  <span>性别：{{ currentPatient?.gender || '--' }}</span>
                  <span>年龄：{{ currentPatient?.age || '--' }}岁</span>
                  <span>电话：{{ currentPatient?.phone || '--' }}</span>
                </div>
              </div>
            </div>
            <div class="flex items-center">
              <Button type="link" size="small" class="flex items-center">
                <i class="icon-[ph--hand-heart-light]"></i>
                开始接诊
              </Button>
              <Button type="link" size="small" class="flex items-center">
                <i class="icon-[arcticons--mymoney-pro]"></i>
                费用预览
              </Button>
              <Button type="link" size="small" class="flex items-center">
                <i class="icon-[fluent--print-32-light]"></i>
                打印
              </Button>
            </div>
          </div>
        </Card>
        <div class="flex-1">
          <div>
            <Card size="small" class="mt-2">
              <template #title>
                <div class="flex items-center gap-1 text-base font-bold">
                  <span
                    class="icon-[streamline-color--checkup-medical-report-clipboard]"
                  ></span>
                  <span>病历</span>
                </div>
              </template>
              <template #extra>
                <div class="flex w-[300px] items-center justify-end">
                  <Button type="link" size="small" class="flex items-center">
                    <i class="icon-[tabler--hand-finger]"></i>
                    选择模板
                  </Button>
                  <Button type="link" size="small" class="flex items-center">
                    <i class="icon-[material-symbols-light--add-rounded]"></i>
                    添加一行
                  </Button>
                </div>
              </template>
              <div>
                <vxe-table
                  :data="defaultTemplate.fieldList"
                  :show-header="false"
                  :edit-config="editConfig"
                  border
                  stripe
                  size="large"
                >
                  <vxe-column field="fieldName" title="行名称" width="100px">
                    <template #default="{ row }">
                      <span :class="{ required: isTrue(row.required) }">
                        {{ row.fieldName }}
                      </span>
                    </template>
                  </vxe-column>
                  <vxe-column
                    field="fieldValue"
                    title="输入框"
                    :edit-render="{
                      autoFocus: true,
                      placeholder: '请在此处输入或快速选择...',
                    }"
                  >
                    <template #edit="{ row }">
                      <InputSearch v-model:value="row.fieldValue" allow-clear>
                        <template #enterButton>
                          <Button
                            type="primary"
                            class="flex items-center"
                            @click="showModal(row)"
                          >
                            <i
                              class="icon-[mingcute--finger-tap-line] mr-1"
                            ></i>
                            快速选择
                          </Button>
                        </template>
                      </InputSearch>
                    </template>
                  </vxe-column>
                </vxe-table>
              </div>
            </Card>
            <Card size="small" class="mt-2">
              <template #title>
                <div class="flex items-center gap-1 text-base font-bold">
                  <span
                    class="icon-[material-symbols-light--prescriptions] text-[#4147D6]"
                  ></span>
                  <span>处方</span>
                </div>
              </template>
              <template #extra>
                <div class="flex items-center">
                  <Button
                    type="link"
                    size="small"
                    class="flex items-center"
                    @click="handleAddRow"
                  >
                    <i class="icon-[material-symbols-light--add-rounded]"></i>
                    添加一行
                  </Button>
                </div>
              </template>
              <PrescriptionTable
                ref="prescriptionTable"
                @select="handleDrugSelect"
              />
            </Card>
          </div>
        </div>
      </div>
      <Card size="small" class="w-[20%]">
        <Tabs block>
          <TabPane v-for="item in tabList" :key="item.key" :tab="item.label">
            <Empty class="mt-[90%]" />
          </TabPane>
        </Tabs>
      </Card>
    </div>
    <!-- 快速选择弹窗 -->
    <Modal
      v-if="currentRow"
      v-model:open="modalVisible[currentRow.id]"
      title="快速选择"
      width="800px"
      destroy-on-close
      :body-style="{ height: '600px', overflow: 'hidden' }"
      centered
      :mask-closable="false"
    >
      <div class="flex h-full w-full flex-col">
        <InputSearch
          placeholder="请输入关键词搜索"
          allow-clear
          enter-button
          class="mb-4"
          v-model:value="searchKeyword"
        />
        <div
          v-if="currentRow.fieldDataList.length > 0"
          class="flex-1 overflow-auto"
        >
          <div
            v-if="
              Object.keys(getGroupedData(currentRow.fieldDataList)).length > 0
            "
            class="flex flex-col gap-4"
          >
            <div
              v-for="(items, groupName) in getGroupedData(
                currentRow.fieldDataList,
              )"
              :key="groupName"
              class="flex flex-col"
            >
              <div class="text-base font-bold text-gray-700">
                {{ groupName }}
              </div>
              <div class="mt-2 flex flex-wrap">
                <CheckableTag
                  v-for="item in items"
                  :key="item.id"
                  :checked="selectedValues.includes(item.data)"
                  class="mb-2 mr-2"
                  @change="(checked: boolean) => handleTagSelect(item, checked)"
                >
                  {{ item.data }}
                </CheckableTag>
              </div>
            </div>
          </div>
          <Empty v-else description="无匹配结果" />
        </div>
        <Empty v-else description="暂无数据" />
      </div>
      <template #footer>
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-400">
            已选择: {{ selectedValues.length }}项
          </div>
          <div>
            <Button @click="modalVisible[currentRow.id] = false">取消</Button>
            <Button type="primary" class="ml-2" @click="handleConfirm">
              确定
            </Button>
          </div>
        </div>
      </template>
    </Modal>
  </Page>
</template>

<style scoped lang="scss">
.man-avatar {
  background: #4147d6;
  color: #fff;
}
.woman-avatar {
  background: #f5222d;
  color: #fff;
}
</style>
