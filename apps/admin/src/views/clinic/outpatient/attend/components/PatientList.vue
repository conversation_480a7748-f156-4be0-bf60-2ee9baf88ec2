<script setup lang="ts">
import { ref } from 'vue';

import { Avatar, Button, InputSearch, Tag } from 'ant-design-vue';

interface Patient {
  name: string;
  id: string;
  phone: string;
  gender: string;
  department: string;
  age: number;
  address: string;
  status?: 'completed' | 'pending' | 'processing';
  avatar?: string;
}

interface Props {
  selectedId?: string;
}

interface Emits {
  (e: 'select', patient: Patient): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const searchForm = ref({
  name: '',
});

// 模拟API数据，后续替换为实际接口
const patientList = ref<Patient[]>([
  {
    name: '张女士',
    id: '**********',
    phone: '138****5678',
    gender: '女',
    department: '内科',
    age: 20,
    address: '北京市海淀区',
    status: 'processing',
  },
  {
    name: '李先生',
    id: '**********',
    phone: '139****8765',
    gender: '男',
    department: '骨科',
    age: 35,
    address: '北京市朝阳区',
    status: 'pending',
  },
  {
    name: '王女士',
    id: '**********',
    phone: '137****4321',
    gender: '女',
    department: '妇科',
    age: 28,
    address: '北京市西城区',
    status: 'pending',
  },
  {
    name: '赵先生',
    id: '**********',
    phone: '136****9876',
    gender: '男',
    department: '内科',
    age: 45,
    address: '北京市东城区',
    status: 'pending',
  },
  {
    name: '陈小朋友',
    id: '**********',
    phone: '135****2345',
    gender: '男',
    department: '儿科',
    age: 8,
    address: '北京市海淀区',
    status: 'completed',
  },
  {
    name: '刘奶奶',
    id: '**********',
    phone: '134****6789',
    gender: '女',
    department: '内科',
    age: 68,
    address: '北京市丰台区',
    status: 'completed',
  },
]);

const handleSelect = (patient: Patient) => {
  emit('select', patient);
};

const getAvatarClass = (gender: string) => {
  return gender === '男' ? 'man-avatar' : 'woman-avatar';
};

const getStatusTag = (status?: string) => {
  switch (status) {
    case 'completed': {
      return {
        color: 'default',
        icon: 'icon-[icon-park-outline--check-one]',
        text: '已完成',
      };
    }
    case 'pending': {
      return {
        color: 'warning',
        icon: 'icon-[icon-park-outline--alarm-clock]',
        text: '待诊中',
      };
    }
    case 'processing': {
      return {
        color: 'processing',
        icon: 'icon-[line-md--loading-loop]',
        text: '就诊中',
      };
    }
    default: {
      return {
        color: 'warning',
        icon: 'icon-[icon-park-outline--alarm-clock]',
        text: '待诊中',
      };
    }
  }
};
</script>

<template>
  <div class="w-full">
    <div class="mb-4 flex items-center justify-between gap-2">
      <div class="cis-title text-base-im">患者列表</div>
      <div>
        <Button type="primary" class="flex items-center">
          <i class="icon-[ic--round-plus]" mr-1></i>
          新建
        </Button>
      </div>
    </div>

    <div class="mb-4">
      <InputSearch
        v-model:value="searchForm.name"
        allow-clear
        placeholder="请输入患者姓名"
        enter-button
      />
    </div>
    <div class="flex flex-col gap-2">
      <div
        v-for="item in patientList"
        :key="item.id"
        class="flex cursor-pointer items-center gap-3 rounded-lg border border-gray-100 bg-white p-2 shadow-sm hover:bg-gray-50"
        :class="{ 'border-primary': selectedId === item.id }"
        @click="handleSelect(item)"
      >
        <Avatar
          :size="36"
          :src="item.avatar"
          class="flex-shrink-0"
          :class="getAvatarClass(item.gender)"
        >
          {{ item.name.charAt(0) }}
        </Avatar>
        <div class="flex flex-1 flex-col">
          <div class="flex items-center justify-between gap-2">
            <span class="text-base font-medium">{{ item.name }}</span>
            <Tag
              :color="getStatusTag(item.status).color"
              class="flex flex-shrink-0 items-center"
            >
              <i :class="getStatusTag(item.status).icon" class="mr-1"></i>
              {{ getStatusTag(item.status).text }}
            </Tag>
          </div>
          <div class="mt-2 flex items-center gap-2 text-xs text-gray-400">
            <div>{{ item.gender }} | {{ item.age }}岁 | {{ item.phone }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.man-avatar {
  background: #4147d6;
}
.woman-avatar {
  background: #f5222d;
}
.border-primary {
  border-color: hsl(var(--primary));
}
</style>
