<!--
使用antd原生Form生成 详细用法参考ant-design-vue Form组件文档
vscode默认配置文件会自动格式化/移除未使用依赖
-->
<script setup lang="ts">
import type { RuleObject } from 'ant-design-vue/es/form';

import type { DrugForm } from '#/api/clinic/pharmacy/drug/model';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep, getPopupContainer } from '@vben/utils';

import {
  Form,
  FormItem,
  Input,
  InputNumber,
  Select,
  Textarea,
} from 'ant-design-vue';
import { pick } from 'lodash-es';

import { drugAdd, drugInfo, drugUpdate } from '#/api/clinic/pharmacy/drug';
import { getDictOptions } from '#/utils/dict';
import { useBeforeCloseDiff } from '#/utils/popup';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

/**
 * 定义默认值 用于reset
 */
const defaultValues: Partial<DrugForm> = {
  id: undefined,
  name: undefined,
  alias: undefined,
  code: undefined,
  specification: undefined,
  unit: undefined,
  manufacturer: undefined,
  category: undefined,
  price: undefined,
  stock: undefined,
  stockUnit: undefined,
  remark: undefined,
};

/**
 * 表单数据ref
 */
const formData = ref(defaultValues);

type AntdFormRules<T> = Partial<Record<keyof T, RuleObject[]>> & {
  [key: string]: RuleObject[];
};
/**
 * 表单校验规则
 */
const formRules = ref<AntdFormRules<DrugForm>>({
  name: [{ required: true, message: '药品不能为空' }],
  alias: [{ required: true, message: '拼音码不能为空' }],
  unit: [{ required: true, message: '单位不能为空' }],
  category: [{ required: true, message: '药品分类不能为空' }],
  price: [{ required: true, message: '单价不能为空' }],
});

/**
 * useForm解构出表单方法
 */
const { validate, validateInfos, resetFields } = Form.useForm(
  formData,
  formRules,
);

function customFormValueGetter() {
  return JSON.stringify(formData.value);
}

const { onBeforeClose, markInitialized, resetInitialized } = useBeforeCloseDiff(
  {
    initializedGetter: customFormValueGetter,
    currentGetter: customFormValueGetter,
  },
);

const [BasicDrawer, drawerApi] = useVbenDrawer({
  class: 'w-[50%]',
  fullscreenButton: false,
  onBeforeClose,
  onClosed: handleClosed,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);

    const { id } = drawerApi.getData() as { id?: number | string };
    isUpdate.value = !!id;

    if (isUpdate.value && id) {
      const record = await drugInfo(id);
      // 只赋值存在的字段
      const filterRecord = pick(record, Object.keys(defaultValues));
      formData.value = filterRecord;
    }
    await markInitialized();

    drawerApi.drawerLoading(false);
  },
});

async function handleConfirm() {
  try {
    drawerApi.lock(true);
    await validate();
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(formData.value);
    await (isUpdate.value ? drugUpdate(data) : drugAdd(data));
    resetInitialized();
    emit('reload');
    drawerApi.close();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.lock(false);
  }
}

async function handleClosed() {
  formData.value = defaultValues;
  resetFields();
  resetInitialized();
}
</script>

<template>
  <BasicDrawer :title="title">
    <Form :label-col="{ span: 4 }" class="w-[60%]">
      <FormItem label="药品名称" v-bind="validateInfos.name">
        <Input
          allow-clear
          v-model:value="formData.name"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="拼音码" v-bind="validateInfos.alias">
        <Input
          allow-clear
          v-model:value="formData.alias"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="药品编码" v-bind="validateInfos.code">
        <Input
          allow-clear
          v-model:value="formData.code"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="药品分类" v-bind="validateInfos.category">
        <Select
          v-model:value="formData.category"
          :options="getDictOptions('qili_drug_category')"
          :get-popup-container="getPopupContainer"
          :placeholder="$t('ui.formRules.selectRequired')"
        />
      </FormItem>

      <FormItem label="单位" v-bind="validateInfos.unit">
        <Select
          v-model:value="formData.unit"
          :options="getDictOptions('qili_drug_unit')"
          :get-popup-container="getPopupContainer"
          :placeholder="$t('ui.formRules.selectRequired')"
        />
      </FormItem>
      <FormItem label="单价" v-bind="validateInfos.price">
        <InputNumber
          :min="0"
          v-model:value="formData.price"
          :placeholder="$t('ui.formRules.required')"
        >
          <template #addonAfter>元</template>
        </InputNumber>
      </FormItem>
      <FormItem label="规格" v-bind="validateInfos.specification">
        <Input
          v-model:value="formData.specification"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="生产厂家" v-bind="validateInfos.manufacturer">
        <Input
          v-model:value="formData.manufacturer"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>

      <FormItem label="备注" v-bind="validateInfos.remark">
        <Textarea
          v-model:value="formData.remark"
          :placeholder="$t('ui.formRules.required')"
          :rows="4"
        />
      </FormItem>
    </Form>
  </BasicDrawer>
</template>
