import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'searchValue',
    label: '药品名称',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.QILI_DRUG_CATEGORY 便于维护
      options: getDictOptions('qili_drug_category'),
    },
    fieldName: 'category',
    label: '药品分类',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    type: 'seq',
    width: 60,
  },
  {
    title: '药品名称',
    field: 'name',
  },
  {
    title: '拼音码',
    field: 'alias',
  },
  {
    title: '药品编码',
    field: 'code',
  },

  {
    title: '药品分类',
    field: 'category',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.QILI_DRUG_CATEGORY 便于维护
        return renderDict(row.category, 'qili_drug_category');
      },
    },
  },
  {
    title: '单价',
    field: 'price',
  },
  {
    title: '单位',
    field: 'unit',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.QILI_DRUG_UNIT 便于维护
        return renderDict(row.unit, 'qili_drug_unit');
      },
    },
  },
  {
    title: '当前库存数量',
    field: 'stock',
  },
  {
    title: '库存单位',
    field: 'stockUnit',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.QILI_DRUG_UNIT 便于维护
        return renderDict(row.stockUnit, 'qili_drug_unit');
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];
