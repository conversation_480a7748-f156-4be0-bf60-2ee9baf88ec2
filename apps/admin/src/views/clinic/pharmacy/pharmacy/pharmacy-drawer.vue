<!--
使用antd原生Form生成 详细用法参考ant-design-vue Form组件文档
vscode默认配置文件会自动格式化/移除未使用依赖
-->
<script setup lang="ts">
import type { RuleObject } from 'ant-design-vue/es/form';

import type { PharmacyForm } from '#/api/clinic/pharmacy/pharmacy/model';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep, getPopupContainer } from '@vben/utils';

import { Form, FormItem, Input, Select, Textarea } from 'ant-design-vue';
import { pick } from 'lodash-es';

import {
  pharmacyAdd,
  pharmacyInfo,
  pharmacyUpdate,
} from '#/api/clinic/pharmacy/pharmacy';
import { getDictOptions } from '#/utils/dict';
import { useBeforeCloseDiff } from '#/utils/popup';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

/**
 * 定义默认值 用于reset
 */
const defaultValues: Partial<PharmacyForm> = {
  id: undefined,
  name: undefined,
  code: undefined,
  location: undefined,
  contactPerson: undefined,
  contactPhone: undefined,
  status: undefined,
  remark: undefined,
};

/**
 * 表单数据ref
 */
const formData = ref(defaultValues);

type AntdFormRules<T> = Partial<Record<keyof T, RuleObject[]>> & {
  [key: string]: RuleObject[];
};
/**
 * 表单校验规则
 */
const formRules = ref<AntdFormRules<PharmacyForm>>({
  name: [{ required: true, message: '药房名称不能为空' }],
  code: [{ required: true, message: '药房编码不能为空' }],
  status: [{ required: true, message: '状态不能为空' }],
});

/**
 * useForm解构出表单方法
 */
const { validate, validateInfos, resetFields } = Form.useForm(
  formData,
  formRules,
);

function customFormValueGetter() {
  return JSON.stringify(formData.value);
}

const { onBeforeClose, markInitialized, resetInitialized } = useBeforeCloseDiff(
  {
    initializedGetter: customFormValueGetter,
    currentGetter: customFormValueGetter,
  },
);

const [BasicDrawer, drawerApi] = useVbenDrawer({
  class: 'w-[550px]',
  fullscreenButton: false,
  onBeforeClose,
  onClosed: handleClosed,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);

    const { id } = drawerApi.getData() as { id?: number | string };
    isUpdate.value = !!id;

    if (isUpdate.value && id) {
      const record = await pharmacyInfo(id);
      // 只赋值存在的字段
      const filterRecord = pick(record, Object.keys(defaultValues));
      formData.value = filterRecord;
    }
    await markInitialized();

    drawerApi.drawerLoading(false);
  },
});

async function handleConfirm() {
  try {
    drawerApi.lock(true);
    await validate();
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(formData.value);
    await (isUpdate.value ? pharmacyUpdate(data) : pharmacyAdd(data));
    resetInitialized();
    emit('reload');
    drawerApi.close();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.lock(false);
  }
}

async function handleClosed() {
  formData.value = defaultValues;
  resetFields();
  resetInitialized();
}
</script>

<template>
  <BasicDrawer :title="title">
    <Form :label-col="{ span: 4 }">
      <FormItem label="药房名称" v-bind="validateInfos.name">
        <Input
          v-model:value="formData.name"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="药房编码" v-bind="validateInfos.code">
        <Input
          v-model:value="formData.code"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="所在位置" v-bind="validateInfos.location">
        <Input
          v-model:value="formData.location"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="负责人" v-bind="validateInfos.contactPerson">
        <Input
          v-model:value="formData.contactPerson"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="联系电话" v-bind="validateInfos.contactPhone">
        <Input
          v-model:value="formData.contactPhone"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="状态" v-bind="validateInfos.status">
        <Select
          v-model:value="formData.status"
          :options="getDictOptions('qili_pharmacy_status')"
          :get-popup-container="getPopupContainer"
          :placeholder="$t('ui.formRules.selectRequired')"
        />
      </FormItem>
      <FormItem label="备注" v-bind="validateInfos.remark">
        <Textarea
          v-model:value="formData.remark"
          :placeholder="$t('ui.formRules.required')"
          :rows="4"
        />
      </FormItem>
    </Form>
  </BasicDrawer>
</template>
