<!--
使用antd原生Form生成 详细用法参考ant-design-vue Form组件文档
vscode默认配置文件会自动格式化/移除未使用依赖
-->
<script setup lang="ts">
import type { RuleObject } from 'ant-design-vue/es/form';

import type { InventoryStockInItemForm } from '#/api/clinic/inventory/stockInItem/model';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { Form, FormItem, Input, Textarea } from 'ant-design-vue';
import { pick } from 'lodash-es';

import {
  inventoryStockInItemAdd,
  inventoryStockInItemInfo,
  inventoryStockInItemUpdate,
} from '#/api/clinic/inventory/stockInItem';
import { useBeforeCloseDiff } from '#/utils/popup';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

/**
 * 定义默认值 用于reset
 */
const defaultValues: Partial<InventoryStockInItemForm> = {
  id: undefined,
  stockInId: undefined,
  drugId: undefined,
  batchNo: undefined,
  purchasePrice: undefined,
  quantity: undefined,
  amount: undefined,
  remark: undefined,
};

/**
 * 表单数据ref
 */
const formData = ref(defaultValues);

type AntdFormRules<T> = Partial<Record<keyof T, RuleObject[]>> & {
  [key: string]: RuleObject[];
};
/**
 * 表单校验规则
 */
const formRules = ref<AntdFormRules<InventoryStockInItemForm>>({
  batchNo: [{ required: true, message: '批次号不能为空' }],
  purchasePrice: [{ required: true, message: '进价不能为空' }],
  quantity: [{ required: true, message: '入库数量不能为空' }],
  amount: [{ required: true, message: '小计金额不能为空' }],
  remark: [{ required: true, message: '备注不能为空' }],
});

/**
 * useForm解构出表单方法
 */
const { validate, validateInfos, resetFields } = Form.useForm(
  formData,
  formRules,
);

function customFormValueGetter() {
  return JSON.stringify(formData.value);
}

const { onBeforeClose, markInitialized, resetInitialized } = useBeforeCloseDiff(
  {
    initializedGetter: customFormValueGetter,
    currentGetter: customFormValueGetter,
  },
);

const [BasicDrawer, drawerApi] = useVbenDrawer({
  class: 'w-[550px]',
  fullscreenButton: false,
  onBeforeClose,
  onClosed: handleClosed,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);

    const { id } = drawerApi.getData() as { id?: number | string };
    isUpdate.value = !!id;

    if (isUpdate.value && id) {
      const record = await inventoryStockInItemInfo(id);
      // 只赋值存在的字段
      const filterRecord = pick(record, Object.keys(defaultValues));
      formData.value = filterRecord;
    }
    await markInitialized();

    drawerApi.drawerLoading(false);
  },
});

async function handleConfirm() {
  try {
    drawerApi.lock(true);
    await validate();
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(formData.value);
    await (isUpdate.value
      ? inventoryStockInItemUpdate(data)
      : inventoryStockInItemAdd(data));
    resetInitialized();
    emit('reload');
    drawerApi.close();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.lock(false);
  }
}

async function handleClosed() {
  formData.value = defaultValues;
  resetFields();
  resetInitialized();
}
</script>

<template>
  <BasicDrawer :title="title">
    <Form :label-col="{ span: 4 }">
      <FormItem label="入库单ID" v-bind="validateInfos.stockInId">
        <Input
          v-model:value="formData.stockInId"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="药品ID" v-bind="validateInfos.drugId">
        <Input
          v-model:value="formData.drugId"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="批次号" v-bind="validateInfos.batchNo">
        <Input
          v-model:value="formData.batchNo"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="进价" v-bind="validateInfos.purchasePrice">
        <Input
          v-model:value="formData.purchasePrice"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="入库数量" v-bind="validateInfos.quantity">
        <Input
          v-model:value="formData.quantity"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="小计金额" v-bind="validateInfos.amount">
        <Input
          v-model:value="formData.amount"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="备注" v-bind="validateInfos.remark">
        <Textarea
          v-model:value="formData.remark"
          :placeholder="$t('ui.formRules.required')"
          :rows="4"
        />
      </FormItem>
    </Form>
  </BasicDrawer>
</template>
