import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'stockOutNo',
    label: '出库单号',
  },
  {
    component: 'Input',
    fieldName: 'pharmacyId',
    label: '药房ID',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.QILI_INVENTORY_STOCK_OUT_TYPE 便于维护
      options: getDictOptions('qili_inventory_stock_out_type'),
    },
    fieldName: 'type',
    label: '出库类型',
  },
  {
    component: 'Input',
    fieldName: 'relatedId',
    label: '关联ID',
  },
  {
    component: 'Input',
    fieldName: 'totalAmount',
    label: '出库总金额',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'stockOutTime',
    label: '出库时间',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      optionType: 'button',
    },
    fieldName: 'status',
    label: '状态',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键ID',
    field: 'id',
  },
  {
    title: '出库单号',
    field: 'stockOutNo',
  },
  {
    title: '药房ID',
    field: 'pharmacyId',
  },
  {
    title: '出库类型',
    field: 'type',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.QILI_INVENTORY_STOCK_OUT_TYPE 便于维护
        return renderDict(row.type, 'qili_inventory_stock_out_type');
      },
    },
  },
  {
    title: '关联ID',
    field: 'relatedId',
  },
  {
    title: '出库总金额',
    field: 'totalAmount',
  },
  {
    title: '出库时间',
    field: 'stockOutTime',
  },
  {
    title: '状态',
    field: 'status',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '主键ID',
    fieldName: 'id',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '出库单号',
    fieldName: 'stockOutNo',
    component: 'Input',
  },
  {
    label: '药房ID',
    fieldName: 'pharmacyId',
    component: 'Input',
  },
  {
    label: '出库类型',
    fieldName: 'type',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.QILI_INVENTORY_STOCK_OUT_TYPE 便于维护
      options: getDictOptions('qili_inventory_stock_out_type'),
    },
    rules: 'selectRequired',
  },
  {
    label: '关联ID',
    fieldName: 'relatedId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '出库总金额',
    fieldName: 'totalAmount',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '出库时间',
    fieldName: 'stockOutTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    rules: 'required',
  },
  {
    label: '状态',
    fieldName: 'status',
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      optionType: 'button',
    },
    rules: 'selectRequired',
  },
  {
    label: '备注',
    fieldName: 'remark',
    component: 'Textarea',
  },
];
