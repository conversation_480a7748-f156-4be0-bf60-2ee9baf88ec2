<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { MedicalFieldDataForm } from '#/api/clinic/medical/field-data/model';
import type { MedicalFieldForm } from '#/api/clinic/medical/field/model';

import { ref } from 'vue';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { buildShortUUID, getVxePopupContainer } from '@vben/utils';

import { Input, Modal, Popconfirm, Space, Tabs } from 'ant-design-vue';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import {
  medicalFieldExport,
  medicalFieldList,
  medicalFieldRemove,
} from '#/api/clinic/medical/field';
import { commonDownloadExcel } from '#/utils/file/download';

import DraggableCards from './components/DraggableCards.vue';
import { columns, querySchema } from './data';
import medicalFieldDrawer from './medicalField-drawer.vue';

interface FieldOption {
  id: string;
  name: string;
}

interface FieldGroup {
  id: number;
  name: string;
  options: MedicalFieldDataForm[];
}

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  // columns: columns(),
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await medicalFieldList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'clinic-medicalField-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [MedicalFieldDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: medicalFieldDrawer,
});

function handleAdd() {
  drawerApi.setData({});
  drawerApi.open();
}

async function handleEdit(row: Required<MedicalFieldForm>) {
  drawerApi.setData({ id: row.id });
  drawerApi.open();
}

async function handleDelete(row: Required<MedicalFieldForm>) {
  await medicalFieldRemove(row.id);
  await tableApi.query();
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: Required<MedicalFieldForm>) => row.id);
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await medicalFieldRemove(ids);
      await tableApi.query();
    },
  });
}

function handleDownloadExcel() {
  commonDownloadExcel(
    medicalFieldExport,
    '病历字段字典数据',
    tableApi.formApi.form.values,
    {
      fieldMappingTime: formOptions.fieldMappingTime,
    },
  );
}

const [FieldDataModal, fieldDataModalApi] = useVbenModal({
  class: 'w-[50%] h-[700px]',
  draggable: true,
});

function handleFieldDataClick(row: Required<MedicalFieldForm>) {
  fieldDataModalApi.setData({
    id: row.id,
    title: `配置选项值`,
  });
  fieldDataModalApi.open();
}
const fieldDataList = ref<FieldGroup[]>([
  {
    id: 1,
    name: '默认分组',
    options: [],
  },
]);
const activeGroupId = ref<number>(fieldDataList.value[0]?.id ?? 1);

const addGroupModalVisible = ref(false);
const addGroupName = ref('');

function showAddGroupModal() {
  addGroupName.value = '';
  addGroupModalVisible.value = true;
}
function handleAddGroupOk() {
  if (!addGroupName.value.trim()) return;
  const newId = Date.now();
  fieldDataList.value.push({
    id: newId,
    name: addGroupName.value.trim(),
    options: [],
  });
  activeGroupId.value = newId;
  addGroupModalVisible.value = false;
}
function handleAddGroupCancel() {
  addGroupModalVisible.value = false;
}
function addGroup() {
  showAddGroupModal();
}
function removeGroup(groupId: number) {
  const idx = fieldDataList.value.findIndex((g) => g.id === groupId);
  if (idx !== -1) {
    fieldDataList.value.splice(idx, 1);
    // 切换到下一个或上一个tab
    activeGroupId.value =
      fieldDataList.value.length > 0
        ? fieldDataList.value[Math.max(0, idx - 1)].id
        : 1;
  }
}

function editOption(groupId: number, optionId: string) {
  console.log('编辑', groupId, optionId);
}

function addOption(groupId: number) {
  const group = fieldDataList.value.find((g) => g.id === groupId);
  if (!group) return;

  group.options.push({
    id: buildShortUUID(),
    data: `新选项${buildShortUUID().slice(0, 5)}`,
    sortOrder: group.options.length + 1,
  });
}

function deleteOption(groupId: number, optionId: string) {
  const group = fieldDataList.value.find((g) => g.id === groupId);
  if (!group) return;

  const index = group.options.findIndex(
    (opt: FieldOption) => opt.id === optionId,
  );
  if (index !== -1) {
    group.options.splice(index, 1);
  }
}

function handleSaveOption(item: MedicalFieldDataForm) {
  console.log('保存', item);
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="病历字段字典列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['clinic:medicalField:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['clinic:medicalField:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['clinic:medicalField:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button type="primary" @click.stop="handleFieldDataClick(row)">
            配置选项值
          </ghost-button>
          <ghost-button
            v-access:code="['clinic:medicalField:edit']"
            @click.stop="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['clinic:medicalField:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <MedicalFieldDrawer @reload="tableApi.query()" />
    <FieldDataModal title="配置选项值">
      <div>
        <Tabs
          v-model:active-key="activeGroupId"
          type="editable-card"
          size="small"
          destroy-inactive-tab-pane
          @edit="
            (targetKey, action) => {
              if (action === 'remove') removeGroup(Number(targetKey));
              if (action === 'add') addGroup();
            }
          "
        >
          <template #addIcon>
            <div class="flex items-center">
              <i class="icon-[fluent--add-16-filled] mr-1"></i>添加分组
            </div>
          </template>
          <Tabs.TabPane
            v-for="item in fieldDataList"
            :key="item.id"
            :tab="item.name"
            :closable="fieldDataList.length > 1"
          >
            <DraggableCards
              v-model:field-data="item.options"
              :on-edit="(optionId) => editOption(item.id, optionId)"
              :on-delete="(optionId) => deleteOption(item.id, optionId)"
              :on-add="() => addOption(item.id)"
              :on-save="(item) => handleSaveOption(item)"
            />
          </Tabs.TabPane>
        </Tabs>
      </div>
    </FieldDataModal>
    <Modal
      v-model:visible="addGroupModalVisible"
      title="新建分组"
      @ok="handleAddGroupOk"
      @cancel="handleAddGroupCancel"
      :mask-closable="false"
    >
      <Input
        v-model:value="addGroupName"
        placeholder="请输入分组名称"
        @keyup.enter="handleAddGroupOk"
      />
    </Modal>
  </Page>
</template>

<style lang="scss" scoped>
.ghost {
  transition: all;
  opacity: 0.8;
  scale: 0.9;
  background: #c8ebfb;
  box-shadow: 0 4px 12px #b1c9ff;
}
</style>
