{"name": "@vben/web-antd", "version": "1.4.1", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-antd"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@tinymce/tinymce-vue": "^6.0.1", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "@vxe-ui/plugin-render-antd": "4.0.17", "ant-design-vue": "catalog:", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "dayjs": "catalog:", "echarts": "^5.5.1", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "pinia": "catalog:", "tinymce": "^7.3.0", "unplugin-vue-components": "^0.27.3", "vue": "catalog:", "vue-draggable-plus": "^0.6.0", "vue-router": "catalog:", "vue3-colorpicker": "^2.3.0", "vxe-table": "catalog:"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12"}}